'use client';

import { getUtmParams } from '@/src/app/_functions/analytics/common';
import { trackMixpanelEvent } from '@/src/app/_lib/initMixpanel';

export const useHandleBeginCheckout = () => {
  const trackBeginCheckout = (service: { id: string; name?: string; price?: number }) => {
    try {
      if (typeof window === 'undefined') return;

      const utmData = getUtmParams();
      const serviceName = service.name || '';
      const servicePrice = service.price || 0;

      // Check if we're in homol environment
      const isHomol = process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT === 'homol';
      const eventName = isHomol ? 'begin_checkout-homol' : 'begin_checkout';

      // Initialize dataLayer and tagManagerDataLayer
      window.dataLayer = window.dataLayer || [];
      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

      // Google Tag Manager
      try {
        const eventData = {
          event: eventName,
          ecommerce: {
            currency: 'BRL',
            value: servicePrice,
            items: [
              {
                item_id: service.id,
                item_name: serviceName,
                price: servicePrice,
                quantity: 1,
              },
            ],
          },
          utm_data: utmData,
        };

        // Push to dataLayer
        window.dataLayer.push(eventData);

        // Push to tagManagerDataLayer if it's different from dataLayer
        if (window.tagManagerDataLayer !== window.dataLayer) {
          window.tagManagerDataLayer.push(eventData);
        }
      } catch (error) {
        // Silently handle dataLayer errors
        console.error('Error pushing to dataLayer:', error);
      }

      // Google Analytics 4
      if (typeof window.gtag === 'function') {
        try {
          window.gtag('event', eventName, {
            currency: 'BRL',
            value: servicePrice,
            items: [
              {
                item_id: service.id,
                item_name: serviceName,
                price: servicePrice,
                quantity: 1,
              },
            ],
          });
        } catch (error) {
          // Silently handle gtag errors
          console.error('Error sending event to gtag:', error);
        }
      }

      // Meta Pixel (Facebook)
      if (typeof window.fbq === 'function') {
        try {
          window.fbq('track', 'InitiateCheckout', {
            content_ids: [service.id],
            content_name: serviceName,
            content_type: 'product',
            value: servicePrice,
            currency: 'BRL',
            ...utmData,
          });
        } catch (error) {
          // Silently handle fbq errors
          console.error('Error sending event to fbq:', error);
        }
      }

      // Mixpanel - using the safe tracking function
      trackMixpanelEvent('Begin Checkout', {
        item_id: service.id,
        item_name: serviceName,
        price: servicePrice,
        quantity: 1,
        currency: 'BRL',
        ...utmData,
      });
    } catch (error) {
      // Catch any unexpected errors
      console.error('Unexpected error in trackBeginCheckout:', error);
    }
  };

  return { trackBeginCheckout };
};
